using System.ComponentModel;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text.Json;
using Microsoft.Build.Framework;

namespace PreviewFramework.AppBuildTasks
{
    public class GeneratePreviewAppSettingsTask : Microsoft.Build.Utilities.Task
    {
        [Required]
        public required string ProjectPath { get; set; }

        [Required]
        public required string OutputPath { get; set; }

        [Required]
        public required string PlatformPreviewApplication { get; set; }

        public override bool Execute()
        {
            try
            {
                string projectPath = Path.GetFullPath(ProjectPath);
                string outputPath = Path.GetFullPath(OutputPath);
                string? outputDirectory = Path.GetDirectoryName(outputPath);

                if (outputDirectory is not null && !Directory.Exists(outputDirectory))
                {
                    Directory.CreateDirectory(outputDirectory);
                }

                // Get the connection string from devToolsConnectionSettings.json. That file exists while
                // DevToolsApp is running, which we'll launch if needed.

                string connectionString = "";
                string homeDir = Environment.GetFolderPath(
                    RuntimeInformation.IsOSPlatform(OSPlatform.Windows)
                        ? Environment.SpecialFolder.UserProfile
                        : Environment.SpecialFolder.Personal);
                string configDir = Path.Combine(homeDir, ".previewframework");
                string jsonPath = Path.Combine(configDir, "devToolsConnectionSettings.json");

                // Check if the devtools app is running, launching it if not.
                bool devToolsAppWasRunning = IsDevToolsAppRunning();
                if (!devToolsAppWasRunning)
                {
                    if (!LaunchDevToolsAppWithLock(configDir, jsonPath))
                    {
                        return false;   // Error already logged
                    }
                }
                else
                {
                    Log.LogMessage(MessageImportance.High, "PreviewFramework: devtools is already running");
                }

                if (!File.Exists(jsonPath))
                {
                    Log.LogError($"PreviewFramework: devtools is running, but the {jsonPath} file doesn't exist.");
                    return false;
                }

                try
                {
                    string jsonContent = File.ReadAllText(jsonPath);
                    using (JsonDocument doc = JsonDocument.Parse(jsonContent))
                    {
                        if (doc.RootElement.TryGetProperty("app", out JsonElement appElement) &&
                            appElement.ValueKind == JsonValueKind.String)
                        {
                            connectionString = appElement.GetString() ?? "";
                        }
                    }
                }
                catch
                {
                    // If any error occurs, use empty string as fallback
                    connectionString = "";
                }

                string content = $$"""
// <auto-generated/>
using System.Runtime.CompilerServices;
using System.Threading.Tasks;

namespace PreviewFramework.SharedModel
{
    public static class PreviewApplicationInitializer
    {
        [ModuleInitializer]
        public static void Initialize()
        {
            var previewApp = {{PlatformPreviewApplication}};
            if (previewApp != null)
            {
                previewApp.ProjectPath = @"{{projectPath.Replace("\"", "\"\"")}}";
                previewApp.ToolingConnectionString = @"{{connectionString.Replace("\"", "\"\"")}}";
                {{(connectionString != "" ? "previewApp.StartToolingConnection();" : "// No tooling connection, so not starting connection")}}
            }
        }
    }
}
""";

                File.WriteAllText(outputPath, content);
                return true;
            }
            catch (Exception ex)
            {
                Log.LogErrorFromException(ex);
                return false;
            }
        }

        private static bool IsDevToolsAppRunning()
        {
            try
            {
                Process[] processes = Process.GetProcessesByName("PreviewFramework.DevToolsApp");
                return processes.Length > 0;
            }
            catch
            {
                return false;
            }
        }

        private bool LaunchDevToolsAppWithLock(string configDir, string jsonPath)
        {
            string lockFilePath = Path.Combine(configDir, "devtools-launching.lock");

            if (!Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }

            // Try to create and hold an exclusive lock on the file
            try
            {
                using var lockFileStream = new FileStream(lockFilePath, FileMode.Create, FileAccess.Write, FileShare.None);
                using var writer = new StreamWriter(lockFileStream);

                // Write lock file content
                writer.WriteLine($"Launched by process {Process.GetCurrentProcess().ProcessName} {Process.GetCurrentProcess().Id} at {DateTime.UtcNow:O}");
                writer.Flush();

                Log.LogMessage(MessageImportance.Low, "PreviewFramework: Acquired launch lock, launching devtools app...");

                // Launch the app while holding the exclusive lock
                bool success = LaunchDevToolsApp();
                return success;

                // Lock file will be automatically deleted when the FileStream is disposed
            }
            catch (IOException ex) when (ex.HResult == -2147024864) // File is being used by another process
            {
                Log.LogMessage(MessageImportance.Low, "PreviewFramework: Another process is launching devtools app, waiting...");
                return WaitForLaunchCompletion(lockFilePath, jsonPath);
            }
            catch (Exception ex)
            {
                Log.LogError($"PreviewFramework: Failed to create launch lock file: {ex.Message}");
                return false;
            }
        }

        private bool WaitForLaunchCompletion(string lockFilePath, string jsonPath)
        {
            var timeout = TimeSpan.FromSeconds(10); // Longer timeout for waiting on another process
            var stopwatch = Stopwatch.StartNew();

            while (stopwatch.Elapsed < timeout)
            {
                // Check if the lock file is gone (launch completed)
                if (!File.Exists(lockFilePath))
                {
                    // Lock file is gone, check if the settings file exists
                    if (File.Exists(jsonPath))
                    {
                        Log.LogMessage(MessageImportance.High, "PreviewFramework: devtools launched by a different build task");
                        return true;
                    }
                    else
                    {
                        Log.LogError("PreviewFramework: A different build task finished launching devtools but the settings file doesn't exist");
                        return false;
                    }
                }

                Thread.Sleep(200); // Check every 200ms
            }

            // Timeout reached - the other process might have failed
            Log.LogError("PreviewFramework: Timeout waiting for another build task to launch devtools");
            return false;
        }

        private bool LaunchDevToolsApp()
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "preview-devtools",
                    Arguments = "--launch",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                Log.LogMessage(MessageImportance.High, "Launching preview-devtools app...");

                using var process = Process.Start(startInfo);
                if (process is null)
                {
                    Log.LogError("Failed to start preview-devtools process.");
                    return false;
                }

                process.WaitForExit();

                if (process.ExitCode != 0)
                {
                    string errorOutput = process.StandardError.ReadToEnd();
                    Log.LogError($"preview-devtools failed with exit code {process.ExitCode}: {errorOutput}");
                    return false;
                }

                // Wait for the connection settings file to exist or 5 seconds timeout
                return WaitForConnectionSettingsFile();
            }
            // When the preview-devtools executable is not found, an E_FAIL Win32Exception is thrown with the messaage below.
            // For English systems, match on the message.
            catch (Win32Exception ex) when (ex.Message.Contains("The system cannot find the file specified"))
            {
                Log.LogError("preview-devtools not found.");
                Log.LogError("Install it via e.g.: dotnet tool install -g --prerelease PreviewFramework.DevTools");
                return false;
            }
            // In other cases, including non-English systems, log a more generic message that covers the not installed case too.
            catch (Exception ex)
            {
                Log.LogError($"Error launching preview-devtools: {ex}");
                Log.LogError("Ensure it is installed via e.g.: dotnet tool install -g --prerelease PreviewFramework.DevTools");
                return false;
            }
        }

        private bool WaitForConnectionSettingsFile()
        {
            string homeDir = Environment.GetFolderPath(
                RuntimeInformation.IsOSPlatform(OSPlatform.Windows)
                    ? Environment.SpecialFolder.UserProfile
                    : Environment.SpecialFolder.Personal);
            string configDir = Path.Combine(homeDir, ".previewframework");
            string jsonPath = Path.Combine(configDir, "devToolsConnectionSettings.json");

            var timeout = TimeSpan.FromSeconds(5);
            var stopwatch = Stopwatch.StartNew();

            while (stopwatch.Elapsed < timeout)
            {
                if (File.Exists(jsonPath))
                {
                    return true;
                }
                Thread.Sleep(100); // Check every 100ms
            }

            // Timeout reached - log error message
            Log.LogError($"preview-devtools launched but the {jsonPath} file didn't get created");
            return false;
        }
    }
}
