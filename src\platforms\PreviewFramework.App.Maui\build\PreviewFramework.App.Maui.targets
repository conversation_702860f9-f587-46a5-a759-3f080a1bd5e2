<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <!-- Property to track if preview app settings have been generated for this project -->
  <PropertyGroup>
    <_PreviewAppSettingsGenerated Condition="'$(_PreviewAppSettingsGenerated)' == ''">false</_PreviewAppSettingsGenerated>
  </PropertyGroup>

  <Target Name="GeneratePreviewAppSettings"
          BeforeTargets="CoreCompile"
          Condition="'$(_PreviewAppSettingsGenerated)' != 'true'"
          Inputs="$(MSBuildProjectFullPath)"
          Outputs="$(IntermediateOutputPath)PreviewAppInitializer.g.cs;$(UserProfile)\.previewframework\devToolsConnectionSettings.json">
    <GeneratePreviewAppSettingsTask
      ProjectPath="$(MSBuildProjectFullPath)"
      OutputPath="$(IntermediateOutputPath)PreviewAppInitializer.g.cs"
      PlatformPreviewApplication="PreviewFramework.App.Maui.MauiPreviewApplication.Instance" />
    <ItemGroup>
      <Compile Include="$(IntermediateOutputPath)PreviewAppInitializer.g.cs" />
    </ItemGroup>

    <!-- Mark that preview app settings have been generated for this project -->
    <PropertyGroup>
      <_PreviewAppSettingsGenerated>true</_PreviewAppSettingsGenerated>
    </PropertyGroup>
  </Target>
</Project>
